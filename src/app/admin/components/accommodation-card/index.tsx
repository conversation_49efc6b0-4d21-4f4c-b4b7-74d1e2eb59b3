"use client";

import { anton } from "@/app/fonts";
import { Button } from "@/components/ui/button";
import { formatToKsh } from "@/lib/utils";
import Image from "next/image";
import { useState } from "react";
import { AccommodationData, Category } from "../../types";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import EditAccommodationForm from "./EditForm";

interface AccommodationCardProps {
  accommodation: AccommodationData;
  categories: Category[];
}

const AccommodationCard = ({
  accommodation,
  categories,
}: AccommodationCardProps) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingAccommodation, setEditingAccommodation] =
    useState<AccommodationData | null>(null);

  const handleUpdate = async (updatedAccommodation: AccommodationData) => {
    try {
      //const res = await
      return;
    } catch (error) {
      console.error(`Error updating accommodation: ${error}`);
      return;
    }
  };

  const handleEdit = () => {
    const accommodationDetails = { ...accommodation };
    setEditingAccommodation(accommodationDetails);
    setIsDialogOpen(true);
  };

  return (
    <div>
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className=" md:max-w-4xl">
          <DialogHeader>
            <DialogTitle className={`${anton.className} text-3xl`}>
              Edit Accomodation {accommodation.name}
            </DialogTitle>
            <DialogDescription>
              Please fill out the form below to update the accommodation.
            </DialogDescription>
          </DialogHeader>
          <div className="w-full">
            <EditAccommodationForm
              initialData={editingAccommodation}
              onSubmit={handleUpdate}
              id={accommodation.id}
              categories={categories}
            />
          </div>
        </DialogContent>
      </Dialog>
      <div className="flex flex-col gap-4 bg-white w-full shadow-sm p-4 rounded-md cursor-pointer">
        <div className="flex items-start flex-col gap-[2px]">
          <h4 className={`${anton.className} leading-none font-bold text-lg`}>
            {formatToKsh(accommodation.pricePerNight)}
          </h4>
          <p className="text-sm text-neutral-500">
            {accommodation.bedrooms} bedroom unit
          </p>
        </div>
        {accommodation && accommodation.images && (
          <div className="h-[180px] w-full relative rounded-md">
            <Image
              src={accommodation.images[0].url}
              alt={accommodation.name}
              fill
              priority={true}
              className="object-cover rounded-md"
              sizes="(max-width: 768px) 100vw, 245px"
            />
          </div>
        )}

        <div className="flex flex-col gap-3 items-start">
          <h4
            className={`${anton.className} leading-none font-semibold text-2xl`}
          >
            {accommodation.name}
          </h4>
          <p className="text-sm/4 line-clamp-3 text-neutral-800">
            {accommodation.summary}
          </p>
        </div>
        <div>
          <Button
            onClick={handleEdit}
            type="button"
            size={"default"}
            className="bg-primary rounded-md px-8 cursor-pointer font-bold"
          >
            Details
          </Button>
        </div>
      </div>
    </div>
  );
};

export default AccommodationCard;
