export type Booking = {
  bookingId: string;
  accommodation: string;
  status: "pending" | "depositConfirmed" | "fullAmountPaid" | "cancelled";
  guestName: string;
  guestPhone: string;
  guestEmail: string;
  startDate: string;
  endDate: string;
  totalAmount: number;
  depositAmount: number;
  fullPayment: boolean;
};

export type BookingDetails = {
  accommodationId: string;
  guestFirstName: string;
  guestLastName: string;
  guestPhoneNumber: string;
  guestEmail: string;
  guestCountry: string;
  startDate: string;
  endDate: string;
  numberOfGuests: string;
};

export type PendingBooking = {
  redactedBooking: {
    bookingId: string;
    accommodationId: string;
    startDate: string;
    endDate: string;
    totalAmount: number;
    depositAmount: number;
    numberOfGuests: number;
  };
  accommodation: {
    name: string;
    bedrooms: number;
    features: string[];
    image: string;
    capacity: number;
    description: string;
  };
};

export type BookingAvailabilityData = {
  startDate?: string;
  endDate?: string;
  noOfGuests?: number;
  noOfBedrooms?: number;
  isAvailable?: boolean;
  accommodationId?: string;
  guestFirstName?: string;
  guestLastName?: string;
  guestEmail?: string;
  guestCountry?: string;
  guestPhoneNumber?: string;
  accommodation?: {
    name: string;
    bedrooms: number;
    image: string;
    capacity: number;
    summary: string;
  };
  redactedBooking?: {
    bookingId: string;
    accommodationId: string;
    startDate: string;
    endDate: string;
    totalAmount: number;
    depositAmount: number;
    numberOfGuests: number;
  };
};
