"use client";

import { anton } from "@/app/fonts";
import { CircleArrowRight, CircleArrowLeft } from "lucide-react";
import Image from "next/image";
import { useRef } from "react";

interface AccommodationImages {
  images?: {
    url: string;
  }[];
}

const RoomPhotos = ({ images }: AccommodationImages) => {
  const scrollContainerRef = useRef<HTMLDivElement | null>(null);

  const scrollContainer = (direction: number = 0) => {
    if (scrollContainerRef.current) {
      if (direction === 0) {
        // Reset scroll position to the start (when changing filter)
        scrollContainerRef.current.scrollTo({
          left: 0,
          behavior: "smooth",
        });
      } else {
        // Scroll left or right when clicking on arrows
        scrollContainerRef.current.scrollBy({
          left: direction * 300, // Adjust this value as needed for scrolling distance
          behavior: "smooth",
        });
      }
    }
  };

  if (!images) {
    return <div />;
  }
  return (
    <div className="w-full mb-8">
      <div className="flex items-center justify-between mb-6">
        <h4 className={`${anton.className} text-2xl`}>
          More Accommodation photos
        </h4>
        {images.length > 3 && (
          <div className=" flex justify-end pt-3">
            <div className="flex items-center gap-[1px]">
              <CircleArrowLeft
                onClick={() => scrollContainer(-1)}
                className="h-8 w-8 text-primary/85 hover:text-primary transition-colors duration-300 cursor-pointer"
              />
              <CircleArrowRight
                onClick={() => scrollContainer(1)}
                className="h-8 w-8 text-primary/85 hover:text-primary transition-colors duration-300 cursor-pointer"
              />
            </div>
          </div>
        )}
      </div>
      <div
        ref={scrollContainerRef}
        className="overflow-x-scroll flex gap-[10px]"
      >
        {images.map(({ url }, idx: number) => (
          <div className="relative aspect-[393/238] w-full rounded" key={idx}>
            <Image
              fill
              src={url}
              alt={`image - ${idx + 1}`}
              className="object-cover rounded"
            />
          </div>
        ))}
      </div>
    </div>
  );
};

export default RoomPhotos;
