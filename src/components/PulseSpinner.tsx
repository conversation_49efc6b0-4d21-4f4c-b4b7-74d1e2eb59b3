import Image from "next/image";
import Logo from "../../public/logo.webp";

const PulseSpinner = ({ size = 24 }) => {
  const sizeInRem = `${size / 4}rem`;

  return (
    <div className="relative flex items-center justify-center">
      <style jsx>{`
        @keyframes spin-pulse {
          0% {
            transform: scale(0.8) rotate(0deg);
          }
          50% {
            transform: scale(1.2) rotate(180deg);
          }
          100% {
            transform: scale(0.8) rotate(360deg);
          }
        }
      `}</style>

      {/* Spinner track */}
      <div
        className="absolute rounded-full border-2 border-gray-300 opacity-30"
        style={{
          width: sizeInRem,
          height: sizeInRem,
        }}
      />

      {/* Animated spinner */}
      <div
        className="absolute animate-spin-pulse rounded-full border-t-2 border-primary"
        style={{
          width: sizeInRem,
          height: sizeInRem,
          animation: "spin-pulse 1.5s cubic-bezier(0.4, 0, 0.2, 1) infinite",
        }}
      />

      {/* Centered pulsating logo */}
      <div
        className="animate-pulse duration-500 flex items-center justify-center"
        style={{
          width: sizeInRem,
          height: sizeInRem,
          animation: "pulse 1.5s cubic-bezier(0.4, 0, 0.2, 1) infinite",
        }}
      >
        <Image
          src={Logo}
          alt="Logo"
          width={60}
          height={60}
          className="object-cover rounded-full"
          quality={90}
          loading="eager"
        />
      </div>
    </div>
  );
};

export default PulseSpinner;
