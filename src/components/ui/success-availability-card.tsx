"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { ArrowLeft, ArrowRight } from "lucide-react";
import { anton } from "@/app/fonts";

interface SuccessCardProps {
  onProceedToBook: () => void;
  onBack: () => void;
}

const SuccessAvailabilityCard = ({
  onProceedToBook,
  onBack,
}: SuccessCardProps) => {
  const [showConfetti, setShowConfetti] = useState(false);

  const handleNext = () => {
    onProceedToBook();
  };

  const handleBackClick = () => {
    onBack();
  };

  useEffect(() => {
    // Trigger confetti animation once on mount
    setShowConfetti(true);

    // Clean up animation after it completes
    const timer = setTimeout(() => {
      setShowConfetti(false);
    }, 4000);

    return () => clearTimeout(timer);
  }, []);

  return (
    <div className="relative overflow-hidden max-w-7xl mx-auto">
      {/* Confetti Animation */}
      <div className="absolute inset-0 pointer-events-none z-10 overflow-hidden">
        <ConfettiPieces showConfetti={showConfetti} />
      </div>

      <Card className="relative bg-white border-none shadow-none">
        <CardContent className="p-12 text-center space-y-8">
          {/* Success Icon */}
          <div className="flex justify-center mb-6">
            <div className="w-14 h-14 rounded-full flex items-center justify-center shadow-lg bg-primary">
              <svg
                className="w-7 h-7 text-white"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={3}
                  d="M5 13l4 4L19 7"
                />
              </svg>
            </div>
          </div>

          {/* Title Section */}
          <div>
            <h1
              className={`${anton.className} text-4xl font-bold tracking-tight text-primary mb-6 `}
            >
              Accommodation is Available
            </h1>
            <div className="flex justify-center space-x-2">
              <span
                className="text-2xl animate-bounce"
                style={{ animationDelay: "0s" }}
              >
                🎉
              </span>
              <span
                className="text-2xl animate-bounce"
                style={{ animationDelay: "0.1s" }}
              >
                ✨
              </span>
              <span
                className="text-2xl animate-bounce"
                style={{ animationDelay: "0.2s" }}
              >
                🎊
              </span>
              <span
                className="text-2xl animate-bounce"
                style={{ animationDelay: "0.3s" }}
              >
                🥳
              </span>
              <span
                className="text-2xl animate-bounce"
                style={{ animationDelay: "0.4s" }}
              >
                🎈
              </span>
            </div>
          </div>

          {/* Celebration Message */}
          <div className="max-w-2xl mx-auto">
            <p className="text-gray-600 text-xl font-medium leading-relaxed">
              Fantastic! Your perfect selection is ready and waiting for you.
              Don&apos;t miss out on this amazing opportunity!
            </p>
          </div>

          {/* Decorative Elements */}
          <div className="flex justify-center space-x-8 py-4">
            <div className="flex flex-col items-center space-y-2">
              <div className="w-4 h-4 rounded-full bg-yellow-400 animate-ping"></div>
              <div className="w-2 h-2 rounded-full bg-yellow-300"></div>
            </div>
            <div className="flex flex-col items-center space-y-2">
              <div
                className="w-4 h-4 rounded-full bg-pink-400 animate-ping"
                style={{ animationDelay: "0.5s" }}
              ></div>
              <div className="w-2 h-2 rounded-full bg-pink-300"></div>
            </div>
            <div className="flex flex-col items-center space-y-2">
              <div
                className="w-4 h-4 rounded-full bg-blue-400 animate-ping"
                style={{ animationDelay: "1s" }}
              ></div>
              <div className="w-2 h-2 rounded-full bg-blue-300"></div>
            </div>
            <div className="flex flex-col items-center space-y-2">
              <div
                className="w-4 h-4 rounded-full animate-ping"
                style={{
                  backgroundColor: "oklch(44.79% 0.108 151.33)",
                  animationDelay: "1.5s",
                }}
              ></div>
              <div
                className="w-2 h-2 rounded-full"
                style={{ backgroundColor: "oklch(44.79% 0.108 151.33)" }}
              ></div>
            </div>
          </div>

          <div className="w-full flex items-center justify-between">
            <Button
              onClick={handleBackClick}
              type="button"
              variant={"outline"}
              className="py-6 rounded-full px-4 font-semibold cursor-pointer"
            >
              <span>
                <ArrowLeft className="h-4 w-4" />
              </span>
              Back to Dates
            </Button>

            <Button
              type="button"
              onClick={handleNext}
              className="py-6 rounded-full px-4 font-semibold cursor-pointer"
            >
              Proceed to Details
              <span>
                <ArrowRight className="h-4 w-4" />
              </span>
            </Button>
          </div>
        </CardContent>
      </Card>

      <style jsx>{`
        @keyframes confetti {
          0% {
            opacity: 1;
            transform: translateY(-60px) rotate(0deg) scale(1);
          }
          50% {
            opacity: 1;
            transform: translateY(50vh) rotate(360deg) scale(0.9);
          }
          100% {
            opacity: 0;
            transform: translateY(100vh) rotate(720deg) scale(0.6);
          }
        }

        .animate-confetti {
          animation: confetti ease-out forwards;
        }

        .hover\\:shadow-2xl:hover {
          box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.25);
        }
      `}</style>
    </div>
  );
};

export default SuccessAvailabilityCard;

const ConfettiPieces = ({ showConfetti }: { showConfetti: boolean }) => {
  return (
    <>
      {Array.from({ length: 40 }).map((_, i) => (
        <div
          key={i}
          className={`absolute w-4 h-4 opacity-0 ${
            showConfetti ? "animate-confetti" : ""
          }`}
          style={{
            left: `${Math.random() * 100}%`,
            backgroundColor: [
              "oklch(44.79% 0.108 151.33)", // Primary color
              "#ff6b6b", // Red
              "#4ecdc4", // Teal
              "#45b7d1", // Blue
              "#feca57", // Yellow
              "#ff9ff3", // Pink
              "#96ceb4", // Green
              "#a78bfa", // Purple
            ][Math.floor(Math.random() * 8)],
            animationDelay: `${Math.random() * 2.5}s`,
            animationDuration: `${3 + Math.random() * 2}s`,
            borderRadius: Math.random() > 0.5 ? "50%" : "0%",
          }}
        />
      ))}
    </>
  );
};
