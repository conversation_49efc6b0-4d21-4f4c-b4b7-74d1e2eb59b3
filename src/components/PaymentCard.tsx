"use client";

import { anton } from "@/app/fonts";
import { Card, CardContent } from "./ui/card";
import Image from "next/image";
import { BedDouble, UsersRound } from "lucide-react";
import { useFormStep } from "@/hooks/use-form-step";
import { useEffect, useState } from "react";
import { BookingAvailabilityData } from "@/types";
import PaymentForm from "@/app/(app)/reservation/payment/Payment/PaymentForm";
import { calculateNights, formatDate, formatToKsh } from "@/lib/utils";

interface PaymentCardProps {
  step: number;
}

const PaymentCard = ({ step }: PaymentCardProps) => {
  const activeStep = step;
  const [accommodation, setAccommodation] = useState<
    BookingAvailabilityData["accommodation"] | undefined
  >({
    name: "",
    bedrooms: 0,
    image: "",
    capacity: 0,
    summary: "",
  });
  const [booking, setBooking] = useState<
    BookingAvailabilityData["redactedBooking"] | undefined
  >({
    bookingId: "",
    accommodationId: "",
    startDate: "",
    endDate: "",
    totalAmount: 0,
    depositAmount: 0,
    numberOfGuests: 0,
  });

  const { getLatestState } = useFormStep({
    currentStep: step,
  });

  useEffect(() => {
    const latestState = getLatestState();
    if (latestState && activeStep === 4) {
      setAccommodation(latestState.formData.accommodation);
      setBooking(latestState.formData.redactedBooking);
    } else {
      setAccommodation(undefined);
      setBooking(undefined);
    }
  }, [activeStep, getLatestState]);

  if (!accommodation || !booking) return null;
  return (
    <div className="w-full relative">
      <Card className="relative bg-white border-none shadow-none px-0 w-full overflow-hidden">
        <CardContent className="py-12 px-0">
          <div className="text-center mb-10">
            <h4 className={`${anton.className} text-4xl text-primary mb-2`}>
              Payment information
            </h4>
            <p>Choose your payment method to secure your booking</p>
          </div>
          <div className="grid grid-cols-2 gap-8">
            {/*One click payments and forms*/}
            <div>
              <PaymentForm depositAmount={booking.depositAmount} />
            </div>

            {/*Booking Summary*/}
            <div className="flex flex-col gap-4">
              {/*Accommodation Summary*/}
              <Card className="py-2">
                <CardContent className="px-2 py-0">
                  <div className="flex flex-col sm:flex-row">
                    {/* Image Section */}
                    {accommodation.image && (
                      <div className="relative w-28 h-32 flex-shrink-0 rounded-lg">
                        <Image
                          src={accommodation.image}
                          alt="Accommodation Image"
                          fill
                          priority
                          quality={100}
                          className="object-cover rounded-lg"
                          sizes="(max-width: 640px) 100vw, 128px"
                        />
                      </div>
                    )}

                    {/* Content Section */}
                    <div className="flex-1 ml-4">
                      <div className="flex flex-col gap-2">
                        <div className="">
                          <h5
                            className={`${anton.className} text-xl font-bold  uppercase text-neutral-800 `}
                          >
                            {accommodation.name}
                          </h5>
                        </div>
                        <div>
                          <p className="text-lg font-bold text-primary leading-[18px] mb-0.5">
                            {accommodation.bedrooms} Bedroom Accommodation
                          </p>
                          <p className="text-sm text-neutral-600 line-clamp-2">
                            {accommodation.summary}
                          </p>
                        </div>
                        <div className="flex items-center gap-6">
                          <div className="flex items-center gap-1 text-neutral-600">
                            <UsersRound className="h-4 w-4" />
                            <span className="text-xs ">
                              {accommodation.capacity} Guests
                            </span>
                          </div>
                          <div className="flex items-center gap-1 text-neutral-600">
                            <BedDouble className="h-4 w-4" />
                            <span className="text-xs ">
                              {accommodation.bedrooms} Beds
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
              {/*Stay Summary*/}
              <Card className="py-2">
                <CardContent className="px-2 py-0">
                  <div className="">
                    <div className=" border-b-[0.8px] pb-2 border-primary/30 mb-4">
                      <h5
                        className={`${anton.className} font-bold text-neutral-800 text-xl uppercase`}
                      >
                        Your stay Details
                      </h5>
                    </div>
                    <div className="flex items-center gap-20 mb-4">
                      <div>
                        <p className="text-xs text-neutral-600 mb-0.5">
                          Check In
                        </p>
                        <p className="text-sm text-primary font-semibold">
                          {formatDate(booking.startDate)}
                        </p>
                      </div>
                      <div>
                        <p className="text-xs text-neutral-600 mb-0.5">
                          Check Out
                        </p>
                        <p className="text-sm text-primary font-semibold">
                          {formatDate(booking.endDate)}
                        </p>
                      </div>
                    </div>
                    <div>
                      <p className="text-sm text-neutral-600 mb-0.5">
                        Total length of stay
                      </p>
                      <p className="text-sm text-primary font-semibold">
                        {calculateNights(booking.startDate, booking.endDate)}{" "}
                        nights
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              {/*Stay Summary*/}
              <Card className="py-2">
                <CardContent className="px-2 py-0">
                  <div className="text-primary">
                    <div className=" border-b-[0.8px] pb-2 border-primary/30 mb-4">
                      <h5
                        className={`${anton.className} font-bold text-neutral-800 text-xl uppercase`}
                      >
                        Amount Details
                      </h5>
                    </div>
                    <div className="grid grid-cols-2 items-center mb-4">
                      <h5
                        className={`${anton.className} text-neutral-700 font-bold text-xl uppercase`}
                      >
                        Due Now
                      </h5>
                      <div className="flex justify-end">
                        <p className={`${anton.className} text-lg`}>
                          {formatToKsh(booking.depositAmount)}
                        </p>
                      </div>
                    </div>
                    <div className="grid grid-cols-2 items-center">
                      <h5
                        className={`${anton.className} text-neutral-700 font-bold text-xl uppercase`}
                      >
                        Grand Total
                      </h5>
                      <div className="flex justify-end">
                        <p className={`${anton.className} text-lg`}>
                          {formatToKsh(booking.totalAmount)}
                        </p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default PaymentCard;
