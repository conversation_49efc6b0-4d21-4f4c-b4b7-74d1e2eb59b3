"use client";

import { useState } from "react";

import { z } from "zod";
import { useForm } from "react-hook-form";
import { CalendarIcon } from "lucide-react";
import { type DateRange } from "react-day-picker";
import { addDays, format } from "date-fns";

import { Input } from "@/components/ui/input";
import { Calendar } from "@/components/ui/calendar";
import { zodResolver } from "@hookform/resolvers/zod";
import { allCountryNames } from "@/data";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Button } from "../ui/button";
import { cn } from "@/lib/utils";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";
import { useRouter } from "next/navigation";
import { useReservation } from "@/context/ReservationContext";

const FormSchema = z.object({
  startDate: z.date({ error: "Please select a valid check in" }),
  endDate: z.date({ error: "Please select a valid check out" }),
  country: z.string({ error: "Please select a valid country" }),
  promoCode: z.string().optional(),
});

const DateRangePicker = () => {
  const router = useRouter();
  const { setReservationData } = useReservation();
  const dateToday = new Date();
  const defaultEndDate = addDays(dateToday, 4);
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: dateToday,
    to: defaultEndDate,
  });

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
  });

  function onSubmit(data: z.infer<typeof FormSchema>) {
    const formattedStartDate = format(data.startDate, "yyyy-MM-dd");
    const formattedEndDate = format(data.endDate, "yyyy-MM-dd");
    setReservationData({
      startDate: formattedStartDate,
      endDate: formattedEndDate,
      country: data.country,
      promoCode: data.promoCode || "",
    });
    router.push("/reservation");
  }
  const [isCalendarOpen, setIsCalendarOpen] = useState(false);

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="relative w-full flex px-4 h-full"
      >
        <div className="flex space-x-3 items-center px-4 py-6 w-fit">
          <Popover open={isCalendarOpen} onOpenChange={setIsCalendarOpen}>
            <div className="flex space-x-3 items-center">
              <FormField
                control={form.control}
                name="startDate"
                render={({ field }) => (
                  <FormItem className="flex flex-col relative">
                    <FormLabel className="text-white">Check in</FormLabel>
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant={"outline"}
                          className={cn(
                            "w-[240px] pl-3 text-left font-normal",
                            !field.value && "text-muted-foreground",
                          )}
                          onClick={() => setIsCalendarOpen(true)}
                        >
                          {field.value ? (
                            format(field.value, "PPP")
                          ) : (
                            <span>Arrival Date</span>
                          )}
                          <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <FormMessage className="w-[200px] absolute -bottom-5" />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="endDate"
                render={({ field }) => (
                  <FormItem className="flex flex-col relative">
                    <FormLabel className="text-white">Check Out</FormLabel>
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant={"outline"}
                          className={cn(
                            "w-[240px] pl-3 text-left font-normal",
                            !field.value && "text-muted-foreground",
                          )}
                          onClick={() => setIsCalendarOpen(true)}
                        >
                          {field.value ? (
                            format(field.value, "PPP")
                          ) : (
                            <span>Departure Date</span>
                          )}
                          <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <FormMessage className="w-[200px] absolute -bottom-5" />
                  </FormItem>
                )}
              />
            </div>
            <PopoverContent className="w-auto p-0" align="end">
              <Calendar
                animate
                mode="range"
                numberOfMonths={2}
                defaultMonth={dateToday}
                selected={dateRange}
                onSelect={(range) => {
                  setDateRange(range);
                  if (range?.from) form.setValue("startDate", range.from);
                  if (range?.to) form.setValue("endDate", range.to);
                }}
                min={4}
                max={20}
                disabled={{
                  before: dateToday,
                }}
                className="rounded-lg border z-[500] shadow-sm w-full md:w-fit"
              />
            </PopoverContent>
          </Popover>
          <FormField
            control={form.control}
            name="country"
            render={({ field }) => (
              <FormItem className="flex flex-col relative">
                <FormLabel className="text-white">
                  Country of residence
                </FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger
                      className={cn(
                        "w-[240px] pl-3 text-left font-normal bg-background shadow-xs",
                        !field.value && "text-muted-foreground",
                      )}
                    >
                      <SelectValue placeholder="Where you currently live" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent className="max-h-[300px]">
                    {allCountryNames.map((country, idx) => (
                      <SelectItem key={idx} value={country.toLowerCase()}>
                        {country}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage className="w-[200px] absolute -bottom-5" />
              </FormItem>
            )}
          />
          <FormField
            name="promoCode"
            control={form.control}
            render={({ field }) => (
              <FormItem className="relative flex flex-col">
                <FormLabel className="text-white">Promo Code</FormLabel>
                <FormControl>
                  <Input
                    defaultValue={field.value}
                    onChange={field.onChange}
                    placeholder="Have a promo code?"
                    className="outline-none w-[240px] bg-background shadow-xs"
                  />
                </FormControl>
                <FormMessage className="w-[200px]" />
              </FormItem>
            )}
          />
        </div>
        <div className="h-full flex items-center justify-center w-full py-6">
          <Button
            type="submit"
            className="rounded-md text-primary w-full h-16 bg-slate-50"
          >
            Book Now
          </Button>
        </div>
      </form>
    </Form>
  );
};

export default DateRangePicker;
