"use client";

import { useState, useEffect } from "react";
import Image from "next/image";
import Slider from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";

import Hero1 from "../../../public/hero/hero-1.webp";
import Hero2 from "../../../public/hero/hero-2.webp";
import Hero3 from "../../../public/hero/hero-3.webp";
import Hero4 from "../../../public/hero/hero-4.webp";
import Preloader from "../Loaders/Preloader";
import { anton } from "@/app/fonts";
import { useHero } from "@/context/HeroContext";

const allMedia = [Hero1, Hero2, Hero3, Hero4];

const HeroClient = () => {
  const [imagesLoaded, setImagesLoaded] = useState(
    Array(allMedia.length).fill(false)
  );
  const { allImagesLoaded, setAllImagesLoaded } = useHero();

  const handleImageLoaded = (index: number) => {
    setImagesLoaded((prev) => {
      const newState = [...prev];
      newState[index] = true;
      return newState;
    });
  };

  useEffect(() => {
    if (imagesLoaded.every((loaded) => loaded)) {
      setAllImagesLoaded(true);
    }
  }, [imagesLoaded, setAllImagesLoaded]);

  useEffect(() => {
    if (!allImagesLoaded) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "auto";
    }

    return () => {
      document.body.style.overflow = "auto";
    };
  }, [allImagesLoaded]);

  const settings = {
    infinite: true,
    speed: 9000,
    autoplaySpeed: 8000,
    slidesToShow: 1,
    autoplay: true,
    slidesToScroll: 1,
    cssEase: "linear",
    fade: true,
    arrows: false,
    adaptiveHeight: true,
    pauseOnHover: false,
    swipeToSlide: true,
  };

  return (
    <div className="relative h-[50vh]  md:h-[85vh] w-full">
      <Slider {...settings}>
        {allMedia.map((image, idx) => (
          <div className="relative w-full" key={idx}>
            <div className="relative h-[50vh] w-full md:h-[85vh]">
              <Image
                fill
                className="object-cover"
                priority={true}
                src={image}
                alt="image"
                onLoad={() => handleImageLoaded(idx)}
              />
            </div>
          </div>
        ))}
      </Slider>
      {!allImagesLoaded ? (
        <div className="fixed h-screen left-0 top-0 w-full z-[1000]">
          <Preloader />
        </div>
      ) : (
        <>
          <div className="absolute h-full  inset-0 z-30 bg-black/45 " />
          <div className="absolute md:left-1/2 md:-translate-x-[50%] top-1/2 z-40 max-w-[700px] md:mx-auto -translate-y-[50%] px-5 text-white md:top-1/4 md:translate-y-[30%]">
            <div className="flex flex-col gap-6 md:gap-8">
              <div className="flex flex-col space-y-3">
                <p className="text-center font-bold">Diani&apos;s Escape</p>
                <h4
                  className={`${anton.className} flex flex-col text-7xl leading-[1] tracking-tight text-center`}
                >
                  Experience <span>Serenity at</span>
                  <span>Silent Palms Villa</span>
                </h4>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default HeroClient;
