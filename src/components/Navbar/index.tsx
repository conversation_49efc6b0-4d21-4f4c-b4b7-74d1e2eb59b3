import Link from "next/link";
import Large from "./Large";
import Image from "next/image";

const Navbar = () => {
  return (
    <div className="fixed top-0 w-full z-[100] inset-x-0">
      <nav className="grid grid-cols-1 backdrop-blur-lg md:grid-cols-2 py-1 w-full items-center border border-neutral-400/50 px-2 rounded-md  bg-white/50 shadow-xs">
        <div className=" flex items-center gap-6 ">
          <Link href="/" className="flex items-center space-x-2">
            <Image
              src="/logo.webp"
              alt="logo"
              width={50}
              height={50}
              className="object-cover"
              priority={true}
              quality={100}
            />
          </Link>
          <Large />
        </div>

        <div className=" h-full flex items-center justify-end">
          <Link
            href="/reservation"
            className="cursor-pointer bg-primary px-6 rounded-full py-3 shadow-lg flex text-slate-100 text-sm font-bold items-center"
          >
            Book Now
          </Link>
        </div>
      </nav>
    </div>
  );
};

export default Navbar;
